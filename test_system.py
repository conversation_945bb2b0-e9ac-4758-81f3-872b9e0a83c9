#!/usr/bin/env python3
"""
System Validation Test for AI Companion System
Tests core functionality and integration points.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_companion.main import AICompanionSystem


async def test_system_initialization():
    """Test system initialization."""
    print("🧪 Testing system initialization...")
    
    try:
        system = AICompanionSystem()
        await system.initialize()
        
        print("✅ System initialization successful")
        return system
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return None


async def test_gemini_service(system):
    """Test Gemini API service."""
    print("🧪 Testing Gemini API service...")
    
    try:
        response = await system.gemini_service.generate_response(
            "Hello, this is a test message. How are you today?"
        )
        
        if response and len(response) > 10:
            print("✅ Gemini service working correctly")
            print(f"   Sample response: {response[:100]}...")
            return True
        else:
            print("❌ Gemini service returned empty or invalid response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini service test failed: {e}")
        return False


async def test_emotional_intelligence(system):
    """Test emotional intelligence service."""
    print("🧪 Testing emotional intelligence...")
    
    try:
        # Test with emotional message
        emotional_state = await system.emotional_intelligence.analyze_emotion(
            "I'm feeling really sad and overwhelmed today. Everything seems hopeless.",
            user_id="test_user"
        )
        
        if emotional_state and emotional_state.primary_emotion:
            print("✅ Emotional intelligence working correctly")
            print(f"   Detected emotion: {emotional_state.primary_emotion}")
            print(f"   Intensity: {emotional_state.intensity:.2f}")
            print(f"   Risk level: {emotional_state.risk_level}")
            return True
        else:
            print("❌ Emotional intelligence failed to analyze emotion")
            return False
            
    except Exception as e:
        print(f"❌ Emotional intelligence test failed: {e}")
        return False


async def test_memory_service(system):
    """Test memory service."""
    print("🧪 Testing memory service...")
    
    try:
        # Store a test memory
        memory_stored = await system.memory_service.store_memory(
            user_id="test_user",
            content="This is a test memory for validation",
            interaction_type="conversation"
        )
        
        if memory_stored:
            print("✅ Memory storage working correctly")
            
            # Retrieve memories
            memories = await system.memory_service.get_personal_memories("test_user", limit=5)
            
            if memories:
                print(f"   Retrieved {len(memories)} memories")
                return True
            else:
                print("❌ Memory retrieval failed")
                return False
        else:
            print("❌ Memory storage failed")
            return False
            
    except Exception as e:
        print(f"❌ Memory service test failed: {e}")
        return False


async def test_conversation_service(system):
    """Test conversation service."""
    print("🧪 Testing conversation service...")
    
    try:
        # Process a test conversation
        response = await system.conversation_service.process_message(
            user_id="test_user",
            message="Hello! I'm testing the AI companion system. Can you help me?",
            context={"interface": "test"}
        )
        
        if response and response.get("response"):
            print("✅ Conversation service working correctly")
            print(f"   Response: {response['response'][:100]}...")
            return True
        else:
            print("❌ Conversation service failed to generate response")
            return False
            
    except Exception as e:
        print(f"❌ Conversation service test failed: {e}")
        return False


async def test_crisis_detection(system):
    """Test crisis detection service."""
    print("🧪 Testing crisis detection...")
    
    try:
        # Test with crisis message
        crisis_result = await system.crisis_detection.analyze_message(
            "I don't want to live anymore. I'm thinking about ending it all.",
            user_id="test_user"
        )
        
        if crisis_result and crisis_result.get("is_crisis"):
            print("✅ Crisis detection working correctly")
            print(f"   Crisis detected: {crisis_result['is_crisis']}")
            print(f"   Risk level: {crisis_result['risk_level']}")
            print(f"   Crisis type: {crisis_result.get('crisis_type', 'unknown')}")
            return True
        else:
            print("❌ Crisis detection failed to identify crisis")
            return False
            
    except Exception as e:
        print(f"❌ Crisis detection test failed: {e}")
        return False


async def test_storage_service(system):
    """Test storage service."""
    print("🧪 Testing storage service...")
    
    try:
        # Get storage statistics
        stats = await system.storage_service.get_storage_stats()
        
        if stats:
            print("✅ Storage service working correctly")
            print(f"   Database size: {stats.get('database_size_mb', 0):.2f} MB")
            print(f"   User profiles: {stats.get('user_profiles_count', 0)}")
            print(f"   Personal memories: {stats.get('personal_memories_count', 0)}")
            return True
        else:
            print("❌ Storage service failed to get statistics")
            return False
            
    except Exception as e:
        print(f"❌ Storage service test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive system test."""
    print("🚀 Starting AI Companion System Validation Tests\n")
    
    # Test results
    test_results = {}
    
    # Initialize system
    system = await test_system_initialization()
    if not system:
        print("❌ Cannot proceed with tests - system initialization failed")
        return False
    
    print()
    
    # Run individual tests
    tests = [
        ("Gemini Service", test_gemini_service),
        ("Emotional Intelligence", test_emotional_intelligence),
        ("Memory Service", test_memory_service),
        ("Conversation Service", test_conversation_service),
        ("Crisis Detection", test_crisis_detection),
        ("Storage Service", test_storage_service)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func(system)
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            test_results[test_name] = False
        
        print()
    
    # Cleanup
    try:
        await system.shutdown()
        print("✅ System shutdown completed")
    except Exception as e:
        print(f"⚠️ System shutdown warning: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
    
    print("-"*60)
    print(f"Total: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! System is ready for use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the configuration and try again.")
        return False


async def main():
    """Main test function."""
    try:
        # Set up logging to reduce noise during testing
        logging.getLogger().setLevel(logging.WARNING)
        
        success = await run_comprehensive_test()
        
        if success:
            print("\n🚀 You can now start using the AI Companion System!")
            print("   • Run 'python src/ai_companion/main.py' to start the Gradio interface")
            print("   • Or use the CLI: 'python -m ai_companion.interfaces.cli'")
        else:
            print("\n🔧 Please fix the failing tests before using the system.")
            
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test suite crashed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
